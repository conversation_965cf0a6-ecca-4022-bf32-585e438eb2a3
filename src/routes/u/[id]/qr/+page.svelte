<script lang="ts">
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import QRGenerator from '$lib/components/QRGenerator.svelte';
  import { toast } from '$lib/stores/toast.ts';
  import type { PageData } from './$types';

  export let data: PageData;

  let qrGenerator: QRGenerator;

  function handleCopySuccess() {
    toast.success('QR code copied to clipboard!');
  }

  function handleCopyError() {
    toast.error('Failed to copy QR code');
  }

  function handleDownloadSuccess() {
    toast.success('QR code downloaded successfully!');
  }

  function goBack() {
    goto('/d'); // Go to dashboard or previous page
  }
</script>

<svelte:head>
  <title>QR Code - {data.user?.n || 'User'}</title>
</svelte:head>

<div class="qr-page">
  <div class="header">
    <button class="btn-soft-ghost" on:click={goBack}>
      ← Back
    </button>
    <h1>Your QR Code</h1>
  </div>

  <div class="content">
    <div class="user-info">
      <div class="user-avatar">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
          <circle cx="12" cy="7" r="4"></circle>
        </svg>
      </div>
      <h2>{data.user?.n || 'Unknown User'}</h2>
      <p class="user-id">ID: {$page.params.id}</p>
    </div>

    <div class="qr-section">
      <div class="qr-container">
        <QRGenerator
          bind:this={qrGenerator}
          data={$page.params.id}
          size={300}
          on:copy-success={handleCopySuccess}
          on:copy-error={handleCopyError}
          on:download-success={handleDownloadSuccess}
        />
      </div>
      
      <div class="qr-instructions">
        <h3>How to use this QR code</h3>
        <ul>
          <li>Show this QR code to scan for attendance</li>
          <li>Use the copy button to share the QR code</li>
          <li>Use the download button to save as an image</li>
          <li>Keep this QR code private and secure</li>
        </ul>
      </div>
    </div>

    <div class="actions">
      <div class="action-grid">
        <div class="action-card">
          <div class="action-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
            </svg>
          </div>
          <h4>Copy QR Code</h4>
          <p>Copy the QR code image to your clipboard for easy sharing</p>
        </div>

        <div class="action-card">
          <div class="action-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
              <polyline points="7,10 12,15 17,10"></polyline>
              <line x1="12" y1="15" x2="12" y2="3"></line>
            </svg>
          </div>
          <h4>Download QR Code</h4>
          <p>Save the QR code as a PNG image to your device</p>
        </div>

        <div class="action-card">
          <div class="action-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M9 12l2 2 4-4"></path>
              <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"></path>
              <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"></path>
            </svg>
          </div>
          <h4>Scan for Attendance</h4>
          <p>Present this QR code to attendance scanners at your school</p>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .qr-page {
    min-height: 100vh;
    padding: var(--space-lg);
  }

  .header {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    margin-bottom: var(--space-2xl);
  }

  .header h1 {
    font-size: 2.5rem;
    margin: 0;
    font-weight: 300;
    color: var(--foreground);
  }

  .content {
    max-width: 800px;
    margin: 0 auto;
  }

  .user-info {
    text-align: center;
    margin-bottom: var(--space-2xl);
  }

  .user-avatar {
    width: 80px;
    height: 80px;
    background: var(--accent);
    border-radius: var(--radius-card);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-lg) auto;
    color: var(--accent-foreground);
    box-shadow: var(--shadow-soft);
  }

  .user-info h2 {
    font-size: 1.75rem;
    margin: 0 0 var(--space-sm) 0;
    font-weight: 500;
    color: var(--foreground);
  }

  .user-id {
    font-size: 0.9rem;
    color: var(--muted-foreground);
    font-family: monospace;
    margin: 0;
  }

  .qr-section {
    margin-bottom: var(--space-2xl);
  }

  .qr-container {
    text-align: center;
    margin-bottom: var(--space-2xl);
    padding: var(--space-xl);
    background: var(--card);
    border-radius: var(--radius-card);
    box-shadow: var(--shadow-soft-lg);
    border: 1px solid var(--border);
  }

  .qr-instructions {
    display: flex;
    justify-content: center;
    padding: var(--space-xl);
    background: var(--muted);
    border-radius: var(--radius-card);
  }

  .qr-instructions h3 {
    font-size: 1.25rem;
    margin: 0 0 var(--space-lg) 0;
    color: var(--foreground);
    font-weight: 500;
  }

  .qr-instructions ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: inline-block;
    text-align: left;
  }

  .qr-instructions li {
    padding: var(--space-sm) 0;
    color: var(--muted-foreground);
    position: relative;
    padding-left: var(--space-lg);
  }

  .qr-instructions li::before {
    content: '•';
    color: var(--primary);
    position: absolute;
    left: 0;
    font-weight: bold;
  }

  .actions {
    padding: var(--space-xl);
    background: var(--card);
    border-radius: var(--radius-card);
    box-shadow: var(--shadow-soft);
    border: 1px solid var(--border);
  }

  .action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-lg);
  }

  .action-card {
    text-align: center;
    padding: var(--space-lg);
    border: 1px solid var(--border);
    border-radius: var(--radius-card);
    transition: all 0.2s;
    background: var(--muted);
  }

  .action-card:hover {
    border-color: var(--color-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-soft);
    background: var(--background);
  }

  .action-icon {
    width: 48px;
    height: 48px;
    background: var(--primary);
    color: var(--primary-foreground);
    border-radius: var(--radius-card);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-lg) auto;
    box-shadow: var(--shadow-soft);
  }

  .action-card h4 {
    font-size: 1.1rem;
    margin: 0 0 var(--space-sm) 0;
    color: var(--foreground);
    font-weight: 500;
  }

  .action-card p {
    font-size: 0.9rem;
    color: var(--muted-foreground);
    margin: 0;
    line-height: 1.4;
  }

  @media (max-width: 640px) {
    .qr-page {
      padding: var(--space-md);
    }

    .header {
      gap: var(--space-md);
      margin-bottom: var(--space-xl);
    }

    .header h1 {
      font-size: 2rem;
    }

    .qr-container,
    .qr-instructions,
    .actions {
      padding: var(--space-lg);
    }

    .action-grid {
      grid-template-columns: 1fr;
      gap: var(--space-md);
    }

    .action-card {
      padding: var(--space-lg);
    }
  }
</style>
